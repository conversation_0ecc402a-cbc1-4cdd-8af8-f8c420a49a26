{"name": "nextjs-sports-analyzer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next lint && next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@aws-sdk/client-s3": "^3.821.0", "@aws-sdk/s3-request-presigner": "^3.821.0", "@google/genai": "^1.3.0", "@supabase/supabase-js": "^2.49.8", "aws-sdk": "^2.1692.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.3", "jest": "^29.7.0", "jest-environment-node": "^30.0.0-beta.3", "supertest": "^7.1.1", "tailwindcss": "^4", "ts-jest": "^29.3.4", "typescript": "^5"}}